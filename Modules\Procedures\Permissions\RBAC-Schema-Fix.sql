-- RBAC-Schema-Fix.sql
-- Fix database schema inconsistencies for the permission system

-- [FixUserPermissionsTable] --
-- Fix user_permissions table structure to match code expectations
DO $$
BEGIN
    -- Check if user_permissions table exists with wrong primary key name
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_permissions' 
        AND column_name = 'permission_id'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_permissions' 
        AND column_name = 'user_permission_id'
    ) THEN
        -- Rename permission_id to user_permission_id
        ALTER TABLE user_permissions RENAME COLUMN permission_id TO user_permission_id;
        RAISE NOTICE 'Renamed permission_id to user_permission_id in user_permissions table';
    END IF;
END $$;
-- [End] --

-- [FixUsersTablePasswordSalt] --
-- Add password_salt column to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'password_salt'
    ) THEN
        ALTER TABLE users ADD COLUMN password_salt VARCHAR(255);
        COMMENT ON COLUMN users.password_salt IS 'Password salt for security';
        RAISE NOTICE 'Added password_salt column to users table';
    END IF;
END $$;
-- [End] --

-- [FixUsersTableCreatedDate] --
-- Add created_date column to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'created_date'
    ) THEN
        ALTER TABLE users ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        COMMENT ON COLUMN users.created_date IS 'User creation timestamp';
        RAISE NOTICE 'Added created_date column to users table';
    END IF;
END $$;
-- [End] --

-- [VerifySchemaFixes] --
-- Verify all schema fixes are applied correctly
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('user_permissions', 'users')
AND column_name IN ('user_permission_id', 'permission_id', 'password_salt', 'created_date')
ORDER BY table_name, column_name;
-- [End] --
