using System;
using System.Linq;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Forms.MainForms;
using ProManage.Modules.Services;

namespace ProManage.Tests
{
    /// <summary>
    /// Tests for PermissionManagementForm functionality
    /// Focuses on dropdown loading and form behavior
    /// </summary>
    [TestClass]
    public class PermissionManagementFormTests
    {
        private PermissionManagementForm _form;

        [TestInitialize]
        public void TestInitialize()
        {
            // Create form instance for testing
            _form = new PermissionManagementForm();
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Clean up form resources
            _form?.Dispose();
        }

        [TestMethod]
        public void TestFormCreation()
        {
            // Test that form can be created without errors
            Assert.IsNotNull(_form, "Form should be created successfully");
            Assert.IsFalse(_form.IsDisposed, "Form should not be disposed");
        }

        [TestMethod]
        public void TestRoleDropdownLoading()
        {
            // Test that roles can be loaded into dropdown
            try
            {
                var roles = PermissionService.GetAllRoles();
                Assert.IsTrue(roles.Count > 0, "Should have roles available for dropdown");
                
                Console.WriteLine($"Available roles for dropdown:");
                foreach (var role in roles.Take(5))
                {
                    Console.WriteLine($"- {role.RoleId}: {role.RoleName}");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Failed to load roles for dropdown: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestUserDropdownLoading()
        {
            // Test that users can be loaded into dropdown
            try
            {
                var users = PermissionService.GetAllUsers();
                Assert.IsTrue(users.Count > 0, "Should have users available for dropdown");
                
                Console.WriteLine($"Available users for dropdown:");
                foreach (var user in users)
                {
                    Console.WriteLine($"- {user.UserId}: {user.Username} - {user.FullName}");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Failed to load users for dropdown: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestDropdownDataFormat()
        {
            // Test the format of data that would be loaded into dropdowns
            try
            {
                var roles = PermissionService.GetAllRoles();
                var users = PermissionService.GetAllUsers();
                
                // Test role dropdown format (as used in PermissionManagementForm)
                if (roles.Count > 0)
                {
                    var roleDropdownItems = roles.Select(r => $"{r.RoleId}|{r.RoleName}").ToArray();
                    Assert.IsTrue(roleDropdownItems.Length > 0, "Should have role dropdown items");
                    
                    // Verify format
                    var firstItem = roleDropdownItems[0];
                    Assert.IsTrue(firstItem.Contains("|"), "Role dropdown item should contain separator");
                    
                    var parts = firstItem.Split('|');
                    Assert.AreEqual(2, parts.Length, "Role dropdown item should have ID and name");
                    Assert.IsTrue(int.TryParse(parts[0], out _), "First part should be numeric ID");
                    
                    Console.WriteLine($"Role dropdown format test passed. Sample: {firstItem}");
                }
                
                // Test user dropdown format
                if (users.Count > 0)
                {
                    var userDropdownItems = users.Select(u => $"{u.UserId}|{u.Username} - {u.FullName}").ToArray();
                    Assert.IsTrue(userDropdownItems.Length > 0, "Should have user dropdown items");
                    
                    // Verify format
                    var firstItem = userDropdownItems[0];
                    Assert.IsTrue(firstItem.Contains("|"), "User dropdown item should contain separator");
                    
                    var parts = firstItem.Split('|');
                    Assert.AreEqual(2, parts.Length, "User dropdown item should have ID and display text");
                    Assert.IsTrue(int.TryParse(parts[0], out _), "First part should be numeric ID");
                    
                    Console.WriteLine($"User dropdown format test passed. Sample: {firstItem}");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Dropdown data format test failed: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestPermissionDataLoading()
        {
            // Test that permission data can be loaded for grids
            try
            {
                var roles = PermissionService.GetAllRoles();
                var users = PermissionService.GetAllUsers();
                
                if (roles.Count > 0)
                {
                    var firstRole = roles.First();
                    var rolePermissions = PermissionService.GetRolePermissions(firstRole.RoleId);
                    Assert.IsNotNull(rolePermissions, "Should be able to load role permissions");
                    Console.WriteLine($"Role '{firstRole.RoleName}' has {rolePermissions.Count} permissions");
                }
                
                if (users.Count > 0)
                {
                    var firstUser = users.First();
                    var userPermissions = PermissionService.GetUserPermissions(firstUser.UserId);
                    Assert.IsNotNull(userPermissions, "Should be able to load user permissions");
                    Console.WriteLine($"User '{firstUser.Username}' has {userPermissions.Count} permission overrides");
                    
                    // Test global permissions
                    var globalPermissions = PermissionService.GetGlobalPermissions(firstUser.UserId);
                    Console.WriteLine($"User '{firstUser.Username}' global permissions: {(globalPermissions != null ? "Set" : "Not set")}");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Permission data loading test failed: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestFormDataIntegrity()
        {
            // Test that all data needed by the form can be loaded without errors
            try
            {
                // Simulate what the form does on load
                var roles = PermissionService.GetAllRoles();
                var users = PermissionService.GetAllUsers();
                
                Assert.IsTrue(roles.Count > 0, "Form needs roles to populate dropdown");
                Assert.IsTrue(users.Count > 0, "Form needs users to populate dropdown");
                
                // Test that we can load permissions for the first role and user
                if (roles.Count > 0)
                {
                    var firstRole = roles.First();
                    var rolePermissions = PermissionService.GetRolePermissions(firstRole.RoleId);
                    Assert.IsNotNull(rolePermissions, "Should be able to load role permissions for grid");
                }
                
                if (users.Count > 0)
                {
                    var firstUser = users.First();
                    var userPermissions = PermissionService.GetUserPermissions(firstUser.UserId);
                    var globalPermissions = PermissionService.GetGlobalPermissions(firstUser.UserId);
                    
                    Assert.IsNotNull(userPermissions, "Should be able to load user permissions for grid");
                    // Global permissions can be null, that's OK
                }
                
                Console.WriteLine("Form data integrity test passed - all required data can be loaded");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Form data integrity test failed: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestErrorHandling()
        {
            // Test that the form can handle various error conditions gracefully
            try
            {
                // Test with invalid role ID
                var invalidRolePermissions = PermissionService.GetRolePermissions(-1);
                Assert.IsNotNull(invalidRolePermissions, "Should return empty list for invalid role ID");
                Assert.AreEqual(0, invalidRolePermissions.Count, "Should return empty list for invalid role ID");
                
                // Test with invalid user ID
                var invalidUserPermissions = PermissionService.GetUserPermissions(-1);
                Assert.IsNotNull(invalidUserPermissions, "Should return empty list for invalid user ID");
                Assert.AreEqual(0, invalidUserPermissions.Count, "Should return empty list for invalid user ID");
                
                var invalidGlobalPermissions = PermissionService.GetGlobalPermissions(-1);
                Assert.IsNull(invalidGlobalPermissions, "Should return null for invalid user ID");
                
                Console.WriteLine("Error handling test passed");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Error handling test failed: {ex.Message}");
            }
        }
    }
}
